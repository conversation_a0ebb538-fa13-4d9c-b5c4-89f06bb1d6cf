# 绿盟漏洞扫描系统自动化工具

基于面向对象设计的绿盟系统自动化工具，支持登录、任务查询和漏洞分析的完整工作流程。

## 项目架构

本项目采用模块化设计，包含以下核心组件：

- **login_manager.py** - 登录管理类，负责验证码识别和用户认证
- **task_manager.py** - 任务管理类，负责任务查询和状态过滤
- **vuln_manager.py** - 漏洞管理类，负责漏洞分析和原理扫描过滤
- **example.py** - 完整示例，演示从登录到漏洞分析的完整流程

## 功能特性

### 登录管理 (LoginManager)
- ✅ 自动获取验证码图片和identifier
- ✅ 使用ddddocr进行验证码自动识别
- ✅ 自动执行登录流程
- ✅ 支持重试机制（最多5次）
- ✅ Session管理和Token维护

### 任务管理 (TaskManager)
- ✅ 查询任务列表（支持分页）
- ✅ 获取已完成任务（task_status=15）
- ✅ 按状态过滤任务
- ✅ 任务搜索和统计功能
- ✅ 任务信息展示

### 漏洞管理 (VulnManager)
- ✅ 查询任务漏洞分布信息
- ✅ 过滤原理扫描漏洞（包含"【原理扫描】"标识）
- ✅ 漏洞统计和分析功能
- ✅ 多任务漏洞汇总分析
- ✅ 按风险等级和严重程度分类

## 安装依赖

```bash
pip install -r requirements.txt
```

或者手动安装：

```bash
pip install requests ddddocr
```

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 运行完整示例

```bash
python example.py
```

## 使用方法

### 1. 完整工作流程

```python
from login_manager import GreenLeagueLogin
from task_manager import TaskManager
from vuln_manager import VulnManager

# 步骤1: 登录
login_manager = GreenLeagueLogin(host="************")
success, result = login_manager.auto_login()

if success:
    # 步骤2: 初始化管理器
    session = login_manager.get_session()
    base_url = login_manager.get_base_url()

    task_manager = TaskManager(session, base_url)
    vuln_manager = VulnManager(session, base_url)

    # 步骤3: 查询已完成任务
    success, completed_tasks = task_manager.get_completed_tasks()

    # 步骤4: 分析原理扫描漏洞
    task_ids = [task['task_id'] for task in completed_tasks]
    analysis_result = vuln_manager.analyze_multiple_tasks_vulns(
        task_ids, focus_on_principle=True
    )
```

### 2. 单独使用各个模块

#### 登录管理

```python
from login_manager import GreenLeagueLogin

login_manager = GreenLeagueLogin(host="************")
success, result = login_manager.auto_login(
    username="admin",
    password="encrypted_password",
    max_retries=5
)
```

#### 任务管理

```python
from task_manager import TaskManager

task_manager = TaskManager(session, base_url)

# 获取所有任务
success, all_tasks = task_manager.get_all_tasks()

# 获取已完成任务
success, completed_tasks = task_manager.get_completed_tasks()

# 搜索任务
success, matched_tasks = task_manager.search_tasks_by_name("扫描")

# 显示任务统计
task_manager.get_task_statistics()
```

#### 漏洞管理

```python
from vuln_manager import VulnManager

vuln_manager = VulnManager(session, base_url)

# 获取单个任务的漏洞
success, vulns = vuln_manager.get_all_vulns_for_task(task_id)

# 获取原理扫描漏洞
success, principle_vulns = vuln_manager.get_principle_scan_vulns_for_task(task_id)

# 分析多个任务
analysis_result = vuln_manager.analyze_multiple_tasks_vulns(
    task_ids, focus_on_principle=True
)

# 显示漏洞统计
vuln_manager.display_vuln_statistics(vulns)
```

## 登录流程说明

根据CSV文件分析，登录流程包含以下步骤：

### 1. 获取验证码
- **请求**: `GET /interface/myauth/captcha/`
- **响应**: 包含base64编码的验证码图片和identifier

### 2. 登录验证
- **请求**: `POST /interface/myauth/login`
- **参数**:
  - `username`: 用户名
  - `password`: 加密后的密码
  - `captcha_code`: 验证码识别结果
  - `identifier`: 验证码标识符

## 漏洞分析功能说明

登录成功后，可以进行以下漏洞分析操作：

### 1. 查询任务列表
- **请求**: `POST /interface/task/task_list/`
- **参数**: `{"page": 1, "page_size": 100}`
- **功能**: 获取所有任务列表

### 2. 过滤已完成任务
- **条件**: `task_status == 15`
- **功能**: 筛选出状态为已完成的任务

### 3. 查询漏洞分布
- **请求**: `GET /interface/report/sys/vuln-distribution/{task_id}`
- **参数**: 包含task_ids、source、page、size等
- **功能**: 获取指定任务的漏洞分布信息

### 4. 过滤原理扫描漏洞
- **条件**: 漏洞名称包含"原理扫描"或"【原理扫描】"
- **功能**: 筛选出原理扫描类型的漏洞

## 重要参数说明

### 登录参数
- `username`: 默认为 "admin"
- `password`: 加密后的密码，默认为 "U2FsdGVkX18pUgexLHc5Y8RrbtAlXz+3EZrDpYJqFqE="
- `captcha_code`: 由ddddocr自动识别
- `identifier`: 从验证码接口自动获取

### 请求头
脚本会自动设置以下重要的请求头：
- `User-Agent`: 模拟Chrome浏览器
- `Content-Type`: application/json;charset=UTF-8
- `Referer`: 设置正确的来源页面
- `Origin`: 设置正确的源地址

## 运行示例

```bash
# 运行基本示例
python example_usage.py

# 或直接运行主脚本
python auto_login.py
```

## 输出示例

```
==================================================
开始绿盟自动登录流程
==================================================

第 1 次尝试登录...
1. 获取验证码...
✓ 成功获取验证码，identifier: kz67lyv7s7olpi0xmjy0qcozuk4obwl3
2. 识别验证码...
✓ 验证码识别结果: fxne
3. 执行登录...
✓ 登录成功: 用户登录成功
  用户名: admin
  用户组: ADMINISTRATOR
  Token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

==================================================
登录成功！
==================================================
```

## 注意事项

1. **密码加密**: 脚本中使用的密码是已经加密的，如需使用其他密码，请确保使用相同的加密方式
2. **网络环境**: 确保能够访问目标主机的HTTP服务
3. **验证码识别**: ddddocr对简单验证码识别率较高，复杂验证码可能需要调整
4. **重试机制**: 默认最多重试3次，可根据需要调整

## 文件结构

```
.
├── login_manager.py       # 登录管理类
├── task_manager.py        # 任务管理类
├── vuln_manager.py        # 漏洞管理类
├── example.py            # 完整示例程序
├── requirements.txt       # 依赖包列表
├── README.md             # 说明文档
├── 绿盟登录.csv          # 原始登录流程数据
├── 查询任务和漏洞.csv     # 任务和漏洞查询流程数据
└── auto_login.py         # 旧版本（已废弃）
```

## 示例程序

### 运行完整示例

```bash
# 运行默认的完整工作流程
python example.py

# 或者运行交互式示例选择
# 修改 example.py 中的 main() 函数调用
```

### 示例输出

```
================================================================================
绿盟漏洞扫描系统 - 完整工作流程示例
================================================================================

步骤1: 登录系统
----------------------------------------
==================================================
开始绿盟自动登录流程
==================================================

第 1 次尝试登录...
1. 获取验证码...
✓ 成功获取验证码，identifier: abc123
2. 识别验证码...
✓ 验证码识别结果: 1234
3. 执行登录...
✓ 登录成功: 用户登录成功
  用户名: admin
  用户组: ADMINISTRATOR
  Token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

步骤2: 初始化管理器
----------------------------------------
✓ 任务管理器初始化完成
✓ 漏洞管理器初始化完成

步骤3: 查询任务统计
----------------------------------------
正在获取所有任务...
✓ 成功获取所有任务，共 15 个

任务统计信息:
------------------------------
已完成: 8 个
运行中: 2 个
等待中: 3 个
未开始: 2 个

步骤4: 获取已完成的任务
----------------------------------------
正在查询已完成的任务...
✓ 找到 8 个已完成的任务

步骤5: 分析原理扫描漏洞
----------------------------------------
开始分析 8 个任务的漏洞信息...
============================================================

正在分析任务 788...
✓ 成功获取任务 788 的漏洞信息，共 25 个漏洞
✓ 任务 788 中发现 5 个原理扫描漏洞
  任务 788: 发现 5 个漏洞

汇总统计:
----------------------------------------
总计发现漏洞: 15 个

原理扫描漏洞统计:
==================================================
总漏洞数: 15

按风险等级统计:
  高危: 3 个
  中危: 8 个
  低危: 4 个

按严重程度分布:
  高危 (≥7.0): 3 个
  中危 (4.0-6.9): 8 个
  低危 (<4.0): 4 个
```

## 扩展功能

可以基于此框架扩展以下功能：
- 添加配置文件支持
- 实现定时任务扫描
- 添加报告导出功能
- 支持多种漏洞过滤条件
- 添加邮件通知功能
- 集成其他安全工具
