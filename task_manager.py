#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
绿盟任务管理类
负责查询任务列表和过滤已完成任务
"""

import requests
import json
from datetime import datetime

class TaskManager:
    """绿盟任务管理类"""
    
    def __init__(self, session, base_url):
        """
        初始化任务管理器
        参数:
            session: 已认证的requests.Session对象
            base_url: 基础URL
        """
        self.session = session
        self.base_url = base_url
    
    def get_task_list(self, page=1, page_size=100):
        """
        获取任务列表
        参数:
            page: 页码，默认为1
            page_size: 每页大小，默认为100
        返回: (success, task_list_data)
        """
        try:
            url = f"{self.base_url}/interface/task/task_list/"
            
            # 构造请求数据
            request_data = {
                "page": page,
                "page_size": page_size
            }
            
            # 设置请求头
            headers = {
                'Content-Type': 'application/json;charset=UTF-8'
            }
            
            response = self.session.post(url, json=request_data, headers=headers)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('code') == 200:
                    # 修正数据结构：实际返回的是 task_list 而不是 results
                    results = data.get('data', {}).get('task_list', [])
                    paginator_data = data.get('data', {}).get('paginator_data', {})
                    total_count = paginator_data.get('total_records', 0)
                    print(f"✓ 成功获取任务列表，当前页 {len(results)} 个任务，总计 {total_count} 个任务")
                    return True, data
                else:
                    print(f"✗ 获取任务列表失败: {data.get('message', '未知错误')}")
                    return False, data
            else:
                print(f"✗ 请求任务列表失败，状态码: {response.status_code}")
                return False, None
                
        except Exception as e:
            print(f"✗ 获取任务列表异常: {str(e)}")
            return False, None
    
    def get_all_tasks(self):
        """
        获取所有任务（自动分页）
        返回: (success, all_tasks_list)
        """
        all_tasks = []
        page = 1
        page_size = 100
        
        print("正在获取所有任务...")
        
        while True:
            success, task_data = self.get_task_list(page=page, page_size=page_size)
            if not success:
                return False, []

            # 修正数据结构
            results = task_data.get('data', {}).get('task_list', [])
            if not results:
                break

            all_tasks.extend(results)

            # 检查是否还有更多页
            paginator_data = task_data.get('data', {}).get('paginator_data', {})
            total_count = paginator_data.get('total_records', 0)
            current_page = paginator_data.get('current_page', page)
            total_pages = paginator_data.get('total_pages', 1)

            if current_page >= total_pages:
                break
            
            page += 1
        
        print(f"✓ 成功获取所有任务，共 {len(all_tasks)} 个")
        return True, all_tasks
    
    def get_completed_tasks(self):
        """
        获取已完成的任务（task_status = 15）
        返回: (success, completed_tasks)
        """
        print("正在查询已完成的任务...")
        
        success, all_tasks = self.get_all_tasks()
        if not success:
            return False, []
        
        # 过滤已完成的任务
        completed_tasks = [task for task in all_tasks if task.get('task_status') == 15]
        
        print(f"✓ 找到 {len(completed_tasks)} 个已完成的任务")
        
        return True, completed_tasks
    
    def get_tasks_by_status(self, status):
        """
        根据状态获取任务
        参数:
            status: 任务状态
        返回: (success, filtered_tasks)
        """
        print(f"正在查询状态为 {status} 的任务...")
        
        success, all_tasks = self.get_all_tasks()
        if not success:
            return False, []
        
        # 过滤指定状态的任务
        filtered_tasks = [task for task in all_tasks if task.get('task_status') == status]
        
        print(f"✓ 找到 {len(filtered_tasks)} 个状态为 {status} 的任务")
        
        return True, filtered_tasks
    
    def display_task_summary(self, tasks):
        """
        显示任务摘要信息
        参数:
            tasks: 任务列表
        """
        if not tasks:
            print("没有任务可显示")
            return
        
        print(f"\n任务摘要 (共 {len(tasks)} 个任务):")
        print("-" * 80)
        
        for i, task in enumerate(tasks, 1):
            task_id = task.get('task_id')
            task_name = task.get('task_name', '未知任务')
            task_status = task.get('task_status')
            create_time = task.get('task_create_time', '未知时间')
            
            # 格式化状态
            status_map = {
                15: "已完成",
                10: "运行中",
                5: "等待中",
                0: "未开始"
            }
            status_text = status_map.get(task_status, f"状态{task_status}")
            
            print(f"{i:3d}. 任务ID: {task_id}")
            print(f"     任务名称: {task_name}")
            print(f"     状态: {status_text}")
            print(f"     创建时间: {create_time}")
            print()
    
    def display_completed_tasks_summary(self):
        """
        显示已完成任务的摘要信息
        """
        success, completed_tasks = self.get_completed_tasks()
        if not success:
            print("获取已完成任务失败")
            return
        
        if not completed_tasks:
            print("没有找到已完成的任务")
            return
        
        print("\n已完成任务详情:")
        print("=" * 80)
        
        for task in completed_tasks:
            task_id = task.get('task_id')
            task_name = task.get('task_name', '未知任务')
            create_time = task.get('task_create_time', '未知时间')
            
            print(f"任务ID: {task_id}")
            print(f"任务名称: {task_name}")
            print(f"创建时间: {create_time}")
            print("-" * 40)
    
    def get_task_statistics(self):
        """
        获取任务统计信息
        返回: dict 包含各状态任务的统计
        """
        success, all_tasks = self.get_all_tasks()
        if not success:
            return {}
        
        statistics = {}
        status_map = {
            15: "已完成",
            10: "运行中", 
            5: "等待中",
            0: "未开始"
        }
        
        # 统计各状态任务数量
        for task in all_tasks:
            status = task.get('task_status')
            status_name = status_map.get(status, f"状态{status}")
            statistics[status_name] = statistics.get(status_name, 0) + 1
        
        # 显示统计信息
        print("\n任务统计信息:")
        print("-" * 30)
        for status_name, count in statistics.items():
            print(f"{status_name}: {count} 个")
        
        return statistics
    
    def search_tasks_by_name(self, keyword):
        """
        根据任务名称关键字搜索任务
        参数:
            keyword: 搜索关键字
        返回: (success, matched_tasks)
        """
        success, all_tasks = self.get_all_tasks()
        if not success:
            return False, []
        
        # 搜索包含关键字的任务
        matched_tasks = [
            task for task in all_tasks 
            if keyword.lower() in task.get('task_name', '').lower()
        ]
        
        print(f"✓ 找到 {len(matched_tasks)} 个包含关键字 '{keyword}' 的任务")
        
        return True, matched_tasks
